<template>
  <div class="pick-bg">
    <div class="pick-title">Pick 你喜欢的公仔</div>
    <div class="container">
      <div
        v-for="(card, idx) in cards"
        :key="card.name"
        class="card"
        :class="{ active: idx === activeIndex, left: getOffset(idx) === -1, right: getOffset(idx) === 1, left2: getOffset(idx) === -2, right2: getOffset(idx) === 2 }"
        :style="{ 'borderColor': borderColors[idx % borderColors.length], zIndex: 10 - Math.abs(getOffset(idx)) }"
      >
        <div class="img">
          <img :src="card.img" :alt="card.name" />
        </div>
        <div class="card-name">{{ card.name }}</div>
      </div>
    </div>
    <div class="pick-btn-wrap">
      <button class="pick-btn" @click="chooseCard(cards[activeIndex])">
        <img src="/img/an1.png" alt="就它了" />
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
const cards = [
  { name: '笑笑虾', img: '/img/ip1.png' },
  { name: '呆呆豚', img: '/img/ip2.png' },
  { name: '灵感菇', img: '/img/33.png' },
  { name: '唉朋鱿', img: '/img/22.png' },
  { name: '叮咚鸡', img: '/img/11.png' }
]
const borderColors = ['#eeb2d2', '#f9d6b7', '#b7e3f9', '#f9f3b7', '#f9c7b7']
const activeIndex = ref(2)
let timer = null
function getOffset(idx) {
  const len = cards.length
  let offset = idx - activeIndex.value
  if (offset > len / 2) offset -= len
  if (offset < -len / 2) offset += len
  return offset
}
onMounted(() => {
  timer = setInterval(() => {
    activeIndex.value = (activeIndex.value + 1) % cards.length
  }, 3000)
})
onUnmounted(() => {
  clearInterval(timer)
})
function chooseCard(card) {
  alert('你选择了 ' + card.name)
}
</script>

<style scoped>
body {
  background: #f7f7fa;
}
.pick-bg {
  width: 100vw;
  height: 100vh;
  min-height: 100svh;
  min-width: 100svw;
  background: linear-gradient(180deg, #e3f0ff 0%, #ffe6e6 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  overflow: hidden;
}
.pick-title {
  font-size: 2.1rem;
  font-weight: bold;
  color: #2d3a4b;
  margin-top: 2.2rem;
  margin-bottom: 8.2rem;
  text-align: center;
  letter-spacing: 0.08em;
  text-shadow: 0 2px 8px #fff8;
}
.container {
  width: 100vw;
  height: 260px;
  max-width: 430px;
  min-height: 180px;
  display: flex;
  justify-content: center;
  align-items: flex-end;
  position: relative;
  margin-bottom: 6.6rem;
  perspective: 1200px;
  perspective-origin: 50% 60%;
}
.card {
  position: absolute;
  left: 50%;
  top: 54%;
  width: 170px;
  height: 220px;
  background: transparent;
  /* border: 4px solid #eeb2d2; */
  border: none;
  border-radius: 18px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.7) rotateY(0deg) translateZ(0px);
  transition: all 0.7s cubic-bezier(.4,1.2,.6,1);
  z-index: 1;
  backface-visibility: hidden;
  will-change: transform, opacity;
}
.card .img {
  width: 130px;
  height: 130px;
  margin-top: 18px;
  margin-bottom: 8px;
  /* background: #fff; */
  box-shadow: 0 0 4px rgba(0,0,0,0.07);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  overflow: hidden;
}
.card .img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 0;
}
.card-name {
  font-size: 1.2rem;
  color: #2d3a4b;
  font-weight: 600;
  margin-top: 2px;
  text-align: center;
  letter-spacing: 0.04em;
}
.card.active {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.18) rotateY(0deg) translateZ(220px);
  z-index: 5;
  box-shadow: 0 8px 32px rgba(80,80,180,0.18), 0 2px 8px rgba(0,0,0,0.10);
}
.card.left {
  opacity: 1;
  transform: translate(-50%, -50%) scale(0.95) rotateY(50deg) translateZ(140px);
  z-index: 4;
}
.card.right {
  opacity: 1;
  transform: translate(-50%, -50%) scale(0.95) rotateY(-50deg) translateZ(140px);
  z-index: 4;
}
.card.left2 {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8) rotateY(80deg) translateZ(40px);
  z-index: 3;
}
.card.right2 {
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.8) rotateY(-80deg) translateZ(40px);
  z-index: 3;
}
.pick-btn-wrap {
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 2.2rem;
}
.pick-btn {
  background: none;
  border: none;
  outline: none;
  padding: 0;
  box-shadow: none;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 220px;
  height: 90px;
  background: transparent;
}
.pick-btn img {
  width: 100%;
  height: auto;
  display: block;
}
@media (max-width: 430px) {
  .container {
    height: 180px;
    min-height: 120px;
  }
  .card {
    width: 110px;
    height: 140px;
  }
  .card .img {
    width: 70px;
    height: 70px;
  }
  .pick-btn {
    width: 150px;
    height: 60px;
    margin-top: 6.2rem;
  }
}
</style> 