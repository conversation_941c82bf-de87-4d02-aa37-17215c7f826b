<template>
  <div class="gym-container">
    <header class="gym-header gym-header-fixed">
      <span>项目：{{ projectName }}</span>
      <span>分数：{{ score }}</span>
      <span>倒计时：{{ timeLeft }}</span>
    </header>
    <main class="gym-main">
      <transition-group name="mole-slide" tag="div">
        <div
          v-for="mole in showMoles"
          :key="mole.id"
          class="mole"
          :style="{ left: mole.x + 'px', top: mole.y + 'px' }"
          @click="hitMole(mole.id)"
        >
          <img src="/img/tcpicha.png" alt="地鼠" class="mole-img" />
        </div>
      </transition-group>
      <!-- 游戏开始321倒计时 -->
      <transition name="countdown-fade">
        <div v-if="showCountdown" class="countdown-overlay">
          <span class="countdown-num">{{ countdownNum }}</span>
        </div>
      </transition>
      <!-- 得分面板动画 -->
      <transition name="score-panel">
        <div v-if="gameOver" class="score-panel-top-wrap">
          <div class="score-panel">
            <img src="/img/tcdefen.png" class="score-bg" alt="得分面板" />
            <div class="score-text">
              <div class="score-title">你最终成绩为</div>
              <div class="score-value">{{ score }}</div>
            </div>
          </div>
        </div>
      </transition>
    </main>
    <!-- 游戏结束底部公仔图片 -->
    <transition name="avatar-fade">
      <div v-if="gameOver" class="avatar-tip-img-wrap">
        <div class="avatar-tip">辛苦了，吃碗面加油。</div>
        <img src="/img/tcchimiandf.png" class="gameover-avatar-bottom" alt="公仔" />
        <button class="honor-btn" @click="onHonorClick">查看荣誉</button>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

// 后端配置参数
const projectName = ref('艺术体操')
const scorePerHit = ref(10)
const countdownTime = ref(60)
const randomCount = ref(3)

const score = ref(0)
const timeLeft = ref(countdownTime.value)
const showMoles = ref([]) // [{x, y, id}]
const gameOver = ref(false)
const showCountdown = ref(true)
const countdownNum = ref(3)
let timer = null
let moleTimer = null
let moleId = 0
let countdownTimer = null

// TODO: 替换为实际获取的token
const token = localStorage.getItem('jwt_token') || ''

const router = useRouter()

async function fetchGameSettings() {
  try {
    console.log('请求接口: /api/game-settings/gymnastics/active')
    const res = await fetch('http://localhost:8081/api/game-settings/gymnastics/active', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    console.log('响应状态:', res.status)
    const text = await res.text()
    console.log('原始响应内容:', text)
    let data
    try {
      data = JSON.parse(text)
    } catch (e) {
      console.error('响应不是合法JSON', e)
      return
    }
    console.log('解析后数据:', data)
    // 兼容返回数组或对象
    let config = Array.isArray(data) ? data[data.length - 1] : data
    if (config && config.projectName && config.isActive !== false) {
      projectName.value = config.projectName
      scorePerHit.value = config.score
      countdownTime.value = config.countdownTime
      randomCount.value = config.randomCount
      timeLeft.value = countdownTime.value
      console.log('赋值成功:', {projectName: projectName.value, scorePerHit: scorePerHit.value, countdownTime: countdownTime.value, randomCount: randomCount.value})
    } else {
      console.warn('未找到有效的体操配置', config)
    }
  } catch (e) {
    // 可选：错误处理
    console.error('获取游戏配置失败', e)
  }
}

function randomMoles() {
  const arr = []
  const minDist = 5.625 // 最小间距，rem (90px / 16)
  let tries = 0
  const yMin = 3.75 // 60px / 16
  const yMax = 31.25 - 5 - 3.75 // 500px/16 - 80px/16 - 60px/16
  const screenWidth = window.innerWidth
  const screenHeight = window.innerHeight
  const moleSize = 6 // 6rem
  while (arr.length < randomCount.value && tries < 100) {
    const x = Math.floor(Math.random() * (screenWidth - moleSize * 16)) + 1.25 * 16 // 20px
    const y = Math.floor(Math.random() * (screenHeight * 0.6 - moleSize * 16)) + yMin * 16
    if (arr.every(m => Math.hypot(m.x - x, m.y - y) > minDist * 16)) {
      arr.push({ x, y, id: moleId++ })
    }
    tries++
  }
  showMoles.value = arr
}

function showRandomMoles() {
  if (gameOver.value) return
  randomMoles()
  moleTimer = setTimeout(() => {
    showMoles.value = []
    moleTimer = setTimeout(showRandomMoles, 300)
  }, 800)
}

function hitMole(id) {
  if (gameOver.value) return
  score.value += scorePerHit.value
  showMoles.value = showMoles.value.filter(m => m.id !== id)
}

function startCountdown() {
  showCountdown.value = true
  countdownNum.value = 3
  countdownTimer = setInterval(() => {
    if (countdownNum.value > 1) {
      countdownNum.value--
    } else {
      clearInterval(countdownTimer)
      showCountdown.value = false
      startGame()
    }
  }, 1000)
}

function startGame() {
  timeLeft.value = countdownTime.value
  score.value = 0
  gameOver.value = false
  showRandomMoles()
  timer = setInterval(() => {
    if (timeLeft.value > 0) {
      timeLeft.value--
      if (timeLeft.value === 0) {
        gameOver.value = true
        showMoles.value = []
        clearInterval(timer)
        clearTimeout(moleTimer)
      }
    }
  }, 1000)
}

function onHonorClick() {
  router.push('/gym-share')
}

onMounted(async () => {
  await fetchGameSettings()
  startCountdown()
})
onUnmounted(() => {
  clearInterval(timer)
  clearTimeout(moleTimer)
  clearInterval(countdownTimer)
})
</script>

<style scoped>
.gym-container {
  width: 100vw;
  height: 100vh;
  background: url('/img/tcbj.png') center center / cover no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
.gym-header {
  width: 100%;
  display: flex;
  justify-content: space-around;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 700;
  color: #222;
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  padding: 0 1rem;
}
.gym-main {
  flex: 1;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: none;
  padding: 0 0 2rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 4.5rem;
}
.mole {
  position: absolute;
  width: 6rem;
  height: 6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  user-select: none;
}
.mole-img {
  width: 5.25rem;
  height: 5.25rem;
  object-fit: contain;
  pointer-events: none;
}
.mole-slide-enter-active, .mole-slide-leave-active {
  transition: all 0.32s cubic-bezier(.4,1.2,.6,1);
}
.mole-slide-enter-from {
  opacity: 0;
  transform: translateY(40px) scale(0.7);
}
.mole-slide-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1);
}
.mole-slide-leave-from {
  opacity: 1;
  transform: translateY(0) scale(1);
}
.mole-slide-leave-to {
  opacity: 0;
  transform: translateY(-40px) scale(0.7);
}
.game-over {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.4rem;
  color: #e74c3c;
  font-weight: bold;
  background: rgba(255,255,255,0.95);
  padding: 2.5rem 3rem;
  border-radius: 1.125rem;
  box-shadow: 0 0.125rem 0.75rem rgba(0,0,0,0.10);
  z-index: 20;
  text-align: center;
  max-width: 80vw;
}
.score-panel-top-wrap {
  position: fixed;
  left: 50%;
  top: -80px;
  transform: translateX(-50%);
  width: 340px;
  max-width: 90vw;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.score-panel {
  position: relative;
  width: 100%;
  margin-top: 80px;
  z-index: 3;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.score-bg {
  width: 100%;
  display: block;
}
.score-text {
  position: absolute;
  top: 50%;
  left: 0;
  width: 100%;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  transform: translateY(10%);
  pointer-events: none;
}
.score-title {
  color: #e74c3c;
  font-size: 1.3rem;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 0.5rem;
  text-align: center;
}
.score-value {
  color: #e74c3c;
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  text-align: center;
}
.score-panel-enter-active, .score-panel-leave-active {
  transition: transform 0.7s cubic-bezier(.4,1.2,.6,1), opacity 0.7s;
}
.score-panel-enter-from {
  transform: translateX(-50%) translateY(-120%);
  opacity: 0;
}
.score-panel-enter-to {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
.score-panel-leave-from {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
}
.score-panel-leave-to {
  transform: translateX(-50%) translateY(-120%);
  opacity: 0;
}
/* 321倒计时动画样式 */
.countdown-overlay {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}
.countdown-num {
  font-size: 6rem;
  color: #e74c3c;
  font-weight: bold;
  text-shadow: 0 0.25rem 1.5rem rgba(231,76,60,0.18);
  animation: countdown-scale 0.8s cubic-bezier(.4,1.2,.6,1);
}
@keyframes countdown-scale {
  0% { transform: scale(0.2); opacity: 0; }
  60% { transform: scale(1.2); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}
.countdown-fade-enter-active, .countdown-fade-leave-active {
  transition: opacity 0.4s;
}
.countdown-fade-enter-from, .countdown-fade-leave-to {
  opacity: 0;
}
.countdown-fade-enter-to, .countdown-fade-leave-from {
  opacity: 1;
}
/* 游戏结束底部公仔图片 */
.avatar-tip-img-wrap {
  position: fixed;
  left: 50%;
  top: 60%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 120;
}
.avatar-tip {
  font-size: clamp(0.9rem, 3vw, 1.2rem);
  color: #e74c3c;
  font-weight: bold;
  margin: 0 0 0.5rem 0;
  text-align: center;
  text-shadow: 0 2px 12px rgba(0,0,0,0.08);
  z-index: 121;
}
.gameover-avatar-bottom {
  width: min(60vw, 60vh, 400px);
  height: min(60vw, 60vh, 400px);
  min-width: 120px;
  min-height: 120px;
  max-width: 90vw;
  max-height: 70vh;
  object-fit: contain;
  z-index: 120;
  pointer-events: none;
  animation: avatar-pop 0.7s cubic-bezier(.4,1.2,.6,1);
  /* 保证动画居中 */
  will-change: transform, opacity;
}
@keyframes avatar-pop {
  0% { transform: scale(0.5); opacity: 0; }
  60% { transform: scale(1.1); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}
.avatar-fade-enter-active, .avatar-fade-leave-active {
  transition: opacity 0.5s;
}
.avatar-fade-enter-from, .avatar-fade-leave-to {
  opacity: 0;
}
.avatar-fade-enter-to, .avatar-fade-leave-from {
  opacity: 1;
}
/* header 层级最高，固定顶部，背景半透明 */
.gym-header-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 80px;
  max-width: 100vw;
  transform: none;
  z-index: 9999;
  background: rgb(255,240,219);
  box-shadow: 0 2px 12px rgba(0,0,0,0.04);
  padding-top: env(safe-area-inset-top);
  border-radius: 0;
  margin-top: 0;
}
/* 让主内容顶部留出 header 高度空间 */
.gym-main {
  margin-top: 4.5rem;
}
.honor-btn {
  margin-top: 2.2rem;
  width: min(80vw, 320px);
  height: 2.8rem;
  font-size: 1.1rem;
  font-weight: 600;
  color: #fff;
  background: linear-gradient(90deg, #ffb347 0%, #ffcc33 100%);
  border: none;
  border-radius: 1.5rem;
  box-shadow: 0 2px 12px rgba(255,204,51,0.10);
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.honor-btn:active {
  opacity: 0.85;
}
</style> 