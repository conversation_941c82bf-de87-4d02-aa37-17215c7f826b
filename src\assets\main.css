@import './base.css';

/* 移动端适配全局样式 */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
  position: fixed;
  width: 100%;
  height: 100%;
}

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

/* 移动端适配媒体查询 */
@media (max-width: 320px) {
  html {
    font-size: 15px;
  }
}

@media (min-width: 430px) {
  html {
    font-size: 17px;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 500px) {
  .container, .pick-container, .gym-container {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .header, .pick-header, .gym-header {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }
  
  .footer, .pick-footer {
    margin-bottom: 0.5rem;
  }
}
