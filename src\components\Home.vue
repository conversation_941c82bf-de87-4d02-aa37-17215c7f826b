<script setup>
import { useRouter } from 'vue-router'
const router = useRouter()
function goToPick() {
  router.push('/pick')
}
</script>

<template>
  <div class="container">
    <header class="header">
      <img alt="logo" class="logo" src="/img/logo.png" />
      <!-- <span class="title">公仔面</span> -->
    </header>
    <main class="main">
      <img class="main-gif" src="/img/1.gif" alt="主视觉图" />
    </main>
    <footer class="footer">
      <button class="btn rule">游戏规则</button>
      <button class="btn go" @click="goToPick">GO</button>
    </footer>
  </div>
</template>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  font-family: 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  overflow: hidden;
  padding: 0;
  /* 安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}
.header {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  padding: 0 1rem;
}
.logo {
  width: 100%;
  max-width: 180px;
  height: auto;
  display: block;
  object-fit: contain;
}
.title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #222;
  letter-spacing: 0.125rem;
}
.main {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 1rem;
  padding: 0 1rem;
}
.main-gif {
  width: 12rem;
  height: 12rem;
  max-width: 70vw;
  max-height: 45vh;
  object-fit: contain;
  border-radius: 1rem;
}
.footer {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 1.25rem 1.5rem 2rem 1.5rem;
  box-sizing: border-box;
  gap: 1rem;
}
.btn {
  flex: 1;
  margin: 0 0.5rem;
  padding: 1.2rem 0;
  font-size: 1.1rem;
  border: none;
  border-radius: 1.5rem;
  background: #41b883;
  color: #fff;
  font-weight: 600;
  letter-spacing: 0.0625rem;
  box-shadow: 0 0.125rem 0.5rem rgba(65,184,131,0.08);
  transition: background 0.2s;
  min-height: 3rem;
}
.btn.rule {
  background: #34495e;
}
.btn.go {
  background: #41b883;
}
.btn:active {
  opacity: 0.8;
}
/* 小屏幕适配 */
@media (max-width: 320px) {
  .title {
    font-size: 1.8rem;
  }
  .main-gif {
    width: 10rem;
    height: 10rem;
  }
  .btn {
    font-size: 1rem;
    padding: 1rem 0;
  }
}

/* 大屏幕适配 */
@media (min-width: 430px) {
  .title {
    font-size: 2.5rem;
  }
  .main-gif {
    width: 14rem;
    height: 14rem;
  }
  .btn {
    font-size: 1.2rem;
    padding: 1.3rem 0;
  }
}
</style> 